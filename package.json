{"name": "astroconnect", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "npx prisma generate && next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "deploy:check": "node scripts/deploy-vercel.js --check", "deploy:schema": "node scripts/deploy-vercel.js --schema", "deploy:seed": "node scripts/deploy-vercel.js --seed"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@prisma/client": "^6.11.1", "@supabase/supabase-js": "^2.50.4", "@types/uuid": "^10.0.0", "@zxing/browser": "^0.1.5", "@zxing/library": "^0.21.3", "bcryptjs": "^3.0.2", "circular-natal-horoscope-js": "^1.1.0", "framer-motion": "^12.23.1", "jsonwebtoken": "^9.0.2", "jsqr": "^1.4.0", "jyotish-calculations": "^1.0.8", "lucide-react": "^0.525.0", "next": "15.3.5", "node-fetch": "^3.3.2", "qrcode": "^1.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20", "@types/qrcode": "^1.5.5", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "prisma": "^6.11.1", "tailwindcss": "^4", "typescript": "^5"}}