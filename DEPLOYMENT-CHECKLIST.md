# AstroConnect Vercel Deployment Checklist

Use this checklist to ensure a smooth deployment of AstroConnect to Vercel.

## 📋 Pre-Deployment Checklist

### ✅ Code Preparation

- [ ] **Code is committed and pushed to GitHub**
  ```bash
  git add .
  git commit -m "Prepare for Vercel deployment"
  git push origin main
  ```

- [ ] **Dependencies are up to date**
  ```bash
  npm audit fix
  npm update
  ```

- [ ] **Build works locally**
  ```bash
  npm run build
  npm start
  ```

- [ ] **Tests are passing**
  ```bash
  npm test
  ```

- [ ] **Deployment helper script passes**
  ```bash
  npm run deploy:check
  ```

### ✅ Configuration Files

- [ ] **`.env.example` exists with all required variables**
- [ ] **`vercel.json` is configured correctly**
- [ ] **`next.config.js` has standalone output disabled**
- [ ] **`package.json` includes postbuild script**

### ✅ Database Setup

- [ ] **PostgreSQL database is created** (Vercel Postgres or external)
- [ ] **Database connection string is ready**
- [ ] **Prisma schema is finalized**
- [ ] **Sample data script is ready** (optional)

### ✅ External Services

- [ ] **Google Gemini API key is obtained**
- [ ] **API keys are tested and working**
- [ ] **Rate limits are understood**

## 🚀 Deployment Steps

### Step 1: Vercel Project Setup

- [ ] **Import project to Vercel**
  - Go to [vercel.com/dashboard](https://vercel.com/dashboard)
  - Click "New Project"
  - Import from GitHub
  - Select AstroConnect repository

- [ ] **Verify build settings**
  - Framework: Next.js
  - Root Directory: `./`
  - Build Command: `npm run build`
  - Output Directory: `.next`

### Step 2: Environment Variables

- [ ] **Add all required environment variables in Vercel**

| Variable | Source | Environment |
|----------|--------|-------------|
| `DATABASE_URL` | Database provider | Production, Preview, Development |
| `GEMINI_API_KEY` | Google AI Studio | Production, Preview, Development |
| `NEXT_PUBLIC_APP_URL` | Vercel app URL | Production, Preview, Development |
| `JWT_SECRET` | Generated secure string | Production, Preview, Development |

- [ ] **Generate JWT secret**
  ```bash
  openssl rand -base64 32
  ```

- [ ] **Update NEXT_PUBLIC_APP_URL with actual Vercel URL**

### Step 3: Database Deployment

- [ ] **Deploy Prisma schema to production database**
  ```bash
  # Set DATABASE_URL to production database
  export DATABASE_URL="your-production-database-url"
  
  # Deploy schema
  npm run deploy:schema
  ```

- [ ] **Seed database with initial data** (optional)
  ```bash
  npm run deploy:seed
  ```

- [ ] **Create admin user**
  ```bash
  node scripts/create-admin.js
  ```

### Step 4: Deploy Application

- [ ] **Trigger deployment**
  - Push to main branch (auto-deploys)
  - Or manually deploy from Vercel dashboard

- [ ] **Monitor build process**
  - Watch build logs in Vercel dashboard
  - Ensure no errors during build

- [ ] **Verify deployment URL**
  - Note the assigned Vercel URL
  - Update environment variables if needed

## 🔍 Post-Deployment Verification

### ✅ Health Checks

- [ ] **API health check passes**
  ```bash
  curl https://your-app.vercel.app/api/health
  ```
  Expected response:
  ```json
  {
    "status": "healthy",
    "timestamp": "...",
    "checks": {
      "database": "ok",
      "api": "ok"
    }
  }
  ```

- [ ] **Database connection works**
  - Visit app URL
  - No database connection errors

- [ ] **Environment variables are loaded**
  - Check that translations work (Gemini API)
  - Verify JWT authentication

### ✅ Functionality Testing

- [ ] **Landing page loads correctly**
  - Visit `https://your-app.vercel.app`
  - Check responsive design
  - Test language switching

- [ ] **QR authentication works**
  - Test camera scanning
  - Test file upload
  - Verify user dashboard loads

- [ ] **Admin panel functions**
  - Login at `https://your-app.vercel.app/admin`
  - Create test user
  - Generate QR codes
  - View analytics

- [ ] **API endpoints respond correctly**
  - Test authentication endpoints
  - Test dashboard data
  - Test translation service

### ✅ Performance & Security

- [ ] **HTTPS is enforced**
  - All requests redirect to HTTPS
  - SSL certificate is valid

- [ ] **Security headers are present**
  - Check with browser dev tools
  - Verify CSP, HSTS, etc.

- [ ] **Rate limiting works**
  - Test API rate limits
  - Verify protection against abuse

- [ ] **Error handling works**
  - Test invalid requests
  - Check error responses

## 🛠️ Troubleshooting

### Common Issues

#### Build Failures

**Issue**: TypeScript errors during build
- **Solution**: Verify `ignoreBuildErrors: true` in `next.config.js`

**Issue**: Prisma client not found
- **Solution**: Ensure `postbuild` script runs `npx prisma generate`

**Issue**: Environment variables not found
- **Solution**: Check variable names and values in Vercel settings

#### Runtime Errors

**Issue**: Database connection fails
- **Solution**: Verify `DATABASE_URL` format and accessibility

**Issue**: API endpoints return 500 errors
- **Solution**: Check Vercel function logs for detailed errors

**Issue**: Translation service fails
- **Solution**: Verify `GEMINI_API_KEY` is valid and has quota

#### Performance Issues

**Issue**: Slow cold starts
- **Solution**: Consider Vercel Pro for better performance

**Issue**: Database query timeouts
- **Solution**: Optimize queries and add connection pooling

### Debug Commands

```bash
# Check deployment logs
vercel logs your-deployment-url

# Test environment variables locally
vercel env pull .env.local

# Run build locally
npm run build && npm start

# Check database connection
npx prisma db push --preview-feature
```

## 📊 Monitoring Setup

### ✅ Vercel Analytics

- [ ] **Enable Web Analytics**
  - Go to project settings
  - Enable analytics
  - Monitor performance metrics

- [ ] **Set up error tracking**
  - Monitor function logs
  - Set up alerts for critical errors

### ✅ Database Monitoring

- [ ] **Monitor database performance**
  - Check connection pool usage
  - Monitor query performance
  - Set up alerts for issues

- [ ] **Regular maintenance**
  - Clean up old translation cache
  - Monitor storage usage
  - Update dependencies regularly

## 🎉 Deployment Complete!

### Final Steps

- [ ] **Document deployment details**
  - Record Vercel URL
  - Note admin credentials
  - Document any custom configurations

- [ ] **Share with stakeholders**
  - Provide access URLs
  - Share admin panel access
  - Document user onboarding process

- [ ] **Set up monitoring**
  - Configure alerts
  - Set up regular health checks
  - Plan maintenance schedule

### Success Metrics

- [ ] **Application loads in < 3 seconds**
- [ ] **QR scanning works on mobile devices**
- [ ] **Language switching is instant**
- [ ] **Admin panel is fully functional**
- [ ] **All API endpoints respond correctly**
- [ ] **Database queries are optimized**

---

**Deployment URL**: `https://your-app.vercel.app`
**Admin Panel**: `https://your-app.vercel.app/admin`
**Health Check**: `https://your-app.vercel.app/api/health`

**Support Resources**:
- [Vercel Documentation](https://vercel.com/docs)
- [Next.js Deployment Guide](https://nextjs.org/docs/deployment)
- [Prisma Deployment Guide](https://www.prisma.io/docs/guides/deployment)
