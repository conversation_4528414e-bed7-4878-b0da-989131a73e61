# AstroConnect - Vercel Deployment Guide

This comprehensive guide will walk you through deploying AstroConnect to Vercel with a PostgreSQL database.

## 📋 Prerequisites

Before starting the deployment process, ensure you have:

- **Vercel Account**: Sign up at [vercel.com](https://vercel.com)
- **GitHub Repository**: Your AstroConnect code pushed to GitHub
- **PostgreSQL Database**: Either:
  - Vercel Postgres (recommended)
  - External PostgreSQL service (Supabase, Railway, etc.)
- **Google Gemini API Key**: From [Google AI Studio](https://makersuite.google.com/app/apikey)

## 🗄️ Database Setup

### Option 1: Vercel Postgres (Recommended)

1. **Create Vercel Postgres Database**
   - Go to your Vercel dashboard
   - Click "Storage" → "Create Database"
   - Select "Postgres" and choose your region
   - Name your database (e.g., `astroconnect-db`)

2. **Get Database Connection String**
   - After creation, go to the database settings
   - Copy the `DATABASE_URL` from the connection string

### Option 2: External PostgreSQL Service

If using an external service like Supabase:

1. **Create Database Instance**
   - Sign up for your chosen service
   - Create a new PostgreSQL database
   - Note the connection details

2. **Get Connection String**
   - Format: `postgresql://username:password@host:port/database`
   - Example: `postgresql://user:<EMAIL>:5432/astroconnect`

## 🚀 Vercel Deployment Steps

### Step 1: Import Project to Vercel

1. **Connect GitHub Repository**
   ```bash
   # If not already done, push your code to GitHub
   git add .
   git commit -m "Prepare for Vercel deployment"
   git push origin main
   ```

2. **Import to Vercel**
   - Go to [vercel.com/dashboard](https://vercel.com/dashboard)
   - Click "New Project"
   - Import your AstroConnect repository from GitHub
   - Select the repository and click "Import"

### Step 2: Configure Build Settings

Vercel should automatically detect Next.js, but verify these settings:

- **Framework Preset**: Next.js
- **Root Directory**: `./` (leave empty if project is in root)
- **Build Command**: `npm run build` (default)
- **Output Directory**: `.next` (default)
- **Install Command**: `npm install` (default)

### Step 3: Environment Variables

Add the following environment variables in Vercel:

1. **Go to Project Settings**
   - In your Vercel project dashboard
   - Click "Settings" → "Environment Variables"

2. **Add Required Variables**

   | Variable | Value | Environment |
   |----------|-------|-------------|
   | `DATABASE_URL` | Your PostgreSQL connection string | Production, Preview, Development |
   | `GEMINI_API_KEY` | Your Google Gemini API key | Production, Preview, Development |
   | `NEXT_PUBLIC_APP_URL` | Your Vercel app URL (e.g., `https://your-app.vercel.app`) | Production, Preview, Development |
   | `JWT_SECRET` | A secure random string (generate with `openssl rand -base64 32`) | Production, Preview, Development |

3. **Optional Variables**
   ```env
   # For enhanced security
   NEXTAUTH_URL=https://your-app.vercel.app
   NEXTAUTH_SECRET=your-nextauth-secret
   
   # For monitoring
   VERCEL_ENV=production
   ```

### Step 4: Database Schema Setup

1. **Install Prisma CLI Locally**
   ```bash
   npm install -g prisma
   ```

2. **Set Environment Variables Locally**
   Create `.env.local`:
   ```env
   DATABASE_URL="your-production-database-url"
   ```

3. **Deploy Database Schema**
   ```bash
   # Generate Prisma client
   npx prisma generate
   
   # Push schema to production database
   npx prisma db push
   
   # (Optional) Seed initial data
   node scripts/populate-sample-data.js
   ```

### Step 5: Deploy Application

1. **Trigger Deployment**
   - Vercel will automatically deploy when you push to your main branch
   - Or manually trigger from Vercel dashboard

2. **Monitor Build Process**
   - Watch the build logs in Vercel dashboard
   - Ensure no errors during build process

## 🔧 Vercel Configuration

### Create `vercel.json` (Optional)

Create a `vercel.json` file in your project root for advanced configuration:

```json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/next"
    }
  ],
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "/api/$1"
    }
  ],
  "env": {
    "NODE_ENV": "production"
  },
  "functions": {
    "src/app/api/**/*.ts": {
      "maxDuration": 30
    }
  }
}
```

### Update `next.config.js` for Vercel

Ensure your `next.config.js` is optimized for Vercel:

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  // Remove standalone output for Vercel
  // output: 'standalone', // Remove this line
  
  // Optimize images for Vercel
  images: {
    domains: ['your-domain.vercel.app'],
    formats: ['image/webp', 'image/avif'],
  },
  
  // Environment variables validation
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  
  // TypeScript and ESLint configuration (keep as is)
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  
  // Other configurations...
  compress: true,
  poweredByHeader: false,
  trailingSlash: false,
  reactStrictMode: true,
};

module.exports = nextConfig;
```

## 🔍 Post-Deployment Verification

### Step 1: Health Check

1. **Test API Endpoints**
   ```bash
   # Check health endpoint
   curl https://your-app.vercel.app/api/health
   
   # Should return:
   # {"status":"healthy","timestamp":"...","checks":{"database":"ok","api":"ok"}}
   ```

2. **Test Database Connection**
   - Visit your app URL
   - Check if the application loads without database errors

### Step 2: Admin Setup

1. **Create Admin User**
   ```bash
   # Run locally with production database
   DATABASE_URL="your-production-url" node scripts/create-admin.js
   ```

2. **Test Admin Login**
   - Go to `https://your-app.vercel.app/admin`
   - Login with created admin credentials

### Step 3: QR Code Testing

1. **Create Test User**
   - Use admin panel to create a test user
   - Generate QR code for the user

2. **Test QR Authentication**
   - Scan QR code or upload QR image
   - Verify user dashboard loads correctly

## 🛠️ Troubleshooting

### Common Issues and Solutions

#### Build Errors

**Issue**: TypeScript or ESLint errors during build
```bash
# Solution: Already configured in next.config.js
typescript: { ignoreBuildErrors: true }
eslint: { ignoreDuringBuilds: true }
```

**Issue**: Prisma client generation errors
```bash
# Solution: Ensure DATABASE_URL is set in Vercel environment variables
# And run prisma generate in build process
```

#### Runtime Errors

**Issue**: Database connection errors
```bash
# Check DATABASE_URL format
# Ensure database is accessible from Vercel
# Verify Prisma schema is deployed
```

**Issue**: Environment variable not found
```bash
# Verify all required env vars are set in Vercel
# Check variable names match exactly
# Ensure variables are set for correct environment
```

#### Performance Issues

**Issue**: Cold start delays
```bash
# Solution: Use Vercel Pro for better performance
# Implement proper caching strategies
# Optimize database queries
```

### Debug Commands

```bash
# Check Vercel deployment logs
vercel logs your-deployment-url

# Test environment variables
vercel env ls

# Run build locally to test
npm run build
npm start
```

## 🔒 Security Considerations

### Production Security Checklist

- [ ] **Environment Variables**: All sensitive data in Vercel env vars
- [ ] **Database Security**: Connection string not exposed in code
- [ ] **API Rate Limiting**: Configured and tested
- [ ] **HTTPS**: Enforced (automatic with Vercel)
- [ ] **CORS**: Properly configured for your domain
- [ ] **JWT Secret**: Strong, unique secret generated
- [ ] **Admin Access**: Secure admin credentials set

### Domain Configuration

1. **Custom Domain** (Optional)
   - Go to Vercel project settings
   - Add your custom domain
   - Update `NEXT_PUBLIC_APP_URL` environment variable

2. **SSL Certificate**
   - Automatic with Vercel
   - Verify HTTPS is working

## 📊 Monitoring and Maintenance

### Vercel Analytics

1. **Enable Analytics**
   - Go to project settings in Vercel
   - Enable Web Analytics
   - Monitor performance metrics

2. **Error Tracking**
   - Monitor function logs in Vercel dashboard
   - Set up alerts for critical errors

### Database Maintenance

```bash
# Regular maintenance tasks
npx prisma db push  # Update schema
node scripts/cleanup-old-data.js  # Clean old translations
```

### Updates and Deployments

```bash
# Deploy updates
git add .
git commit -m "Update: description"
git push origin main  # Auto-deploys to Vercel
```

## 🎉 Success!

Your AstroConnect application should now be successfully deployed on Vercel! 

**Next Steps:**
1. Test all functionality thoroughly
2. Set up monitoring and alerts
3. Configure custom domain (if needed)
4. Share your app with users

**Support:**
- Check Vercel documentation for advanced features
- Monitor application performance and errors
- Keep dependencies updated regularly

---

**Deployment URL**: `https://your-app.vercel.app`
**Admin Panel**: `https://your-app.vercel.app/admin`
**Health Check**: `https://your-app.vercel.app/api/health`

## 📚 Additional Resources

- **[DEPLOYMENT-CHECKLIST.md](./DEPLOYMENT-CHECKLIST.md)**: Step-by-step deployment checklist
- **[.env.example](./.env.example)**: Environment variables template
- **Deployment Helper**: Run `npm run deploy:check` for pre-deployment validation
- **Database Schema**: Run `npm run deploy:schema` to deploy Prisma schema
- **Sample Data**: Run `npm run deploy:seed` to populate initial data

## 🔗 Quick Links

- [Vercel Dashboard](https://vercel.com/dashboard)
- [Vercel Postgres](https://vercel.com/storage/postgres)
- [Google AI Studio](https://makersuite.google.com/app/apikey)
- [Prisma Documentation](https://www.prisma.io/docs)
- [Next.js Deployment](https://nextjs.org/docs/deployment)
