#!/usr/bin/env node

/**
 * AstroConnect Vercel Deployment Helper Script
 * 
 * This script helps prepare and deploy AstroConnect to Vercel
 * Run with: node scripts/deploy-vercel.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, description) {
  log(`\n${description}...`, 'blue');
  try {
    execSync(command, { stdio: 'inherit' });
    log(`✅ ${description} completed successfully`, 'green');
  } catch (error) {
    log(`❌ ${description} failed`, 'red');
    process.exit(1);
  }
}

function checkFile(filePath, description) {
  if (fs.existsSync(filePath)) {
    log(`✅ ${description} exists`, 'green');
    return true;
  } else {
    log(`❌ ${description} not found at ${filePath}`, 'red');
    return false;
  }
}

function checkEnvironmentVariables() {
  log('\n🔍 Checking environment variables...', 'cyan');
  
  const requiredVars = [
    'DATABASE_URL',
    'GEMINI_API_KEY',
    'NEXT_PUBLIC_APP_URL',
    'JWT_SECRET'
  ];
  
  const missingVars = [];
  
  requiredVars.forEach(varName => {
    if (process.env[varName]) {
      log(`✅ ${varName} is set`, 'green');
    } else {
      log(`❌ ${varName} is missing`, 'red');
      missingVars.push(varName);
    }
  });
  
  if (missingVars.length > 0) {
    log('\n⚠️  Missing environment variables. Please set them in:', 'yellow');
    log('   - .env.local for local development', 'yellow');
    log('   - Vercel project settings for deployment', 'yellow');
    log('\nSee .env.example for reference', 'yellow');
    return false;
  }
  
  return true;
}

function checkDependencies() {
  log('\n📦 Checking dependencies...', 'cyan');
  
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  if (!checkFile(packageJsonPath, 'package.json')) {
    return false;
  }
  
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Check for required dependencies
  const requiredDeps = [
    '@prisma/client',
    'next',
    'react',
    'typescript'
  ];
  
  const missingDeps = requiredDeps.filter(dep => 
    !packageJson.dependencies[dep] && !packageJson.devDependencies[dep]
  );
  
  if (missingDeps.length > 0) {
    log(`❌ Missing dependencies: ${missingDeps.join(', ')}`, 'red');
    return false;
  }
  
  log('✅ All required dependencies are present', 'green');
  return true;
}

function checkPrismaSetup() {
  log('\n🗄️  Checking Prisma setup...', 'cyan');
  
  const schemaPath = path.join(process.cwd(), 'prisma', 'schema.prisma');
  if (!checkFile(schemaPath, 'Prisma schema')) {
    return false;
  }
  
  // Check if Prisma client is generated
  const clientPath = path.join(process.cwd(), 'node_modules', '.prisma', 'client');
  if (!fs.existsSync(clientPath)) {
    log('⚠️  Prisma client not generated. Generating now...', 'yellow');
    execCommand('npx prisma generate', 'Generate Prisma client');
  } else {
    log('✅ Prisma client is generated', 'green');
  }
  
  return true;
}

function checkVercelConfig() {
  log('\n⚙️  Checking Vercel configuration...', 'cyan');
  
  const vercelJsonPath = path.join(process.cwd(), 'vercel.json');
  const nextConfigPath = path.join(process.cwd(), 'next.config.js');
  
  checkFile(vercelJsonPath, 'vercel.json');
  checkFile(nextConfigPath, 'next.config.js');
  
  // Check if standalone output is disabled for Vercel
  if (fs.existsSync(nextConfigPath)) {
    const nextConfig = fs.readFileSync(nextConfigPath, 'utf8');
    if (nextConfig.includes("output: 'standalone'") && !nextConfig.includes('// output:')) {
      log('⚠️  Standalone output should be disabled for Vercel deployment', 'yellow');
      log('   Comment out or remove the "output: \'standalone\'" line in next.config.js', 'yellow');
    } else {
      log('✅ Next.js config is optimized for Vercel', 'green');
    }
  }
  
  return true;
}

function runPreDeploymentChecks() {
  log('🚀 AstroConnect Vercel Deployment Helper', 'bright');
  log('==========================================', 'bright');
  
  let allChecksPass = true;
  
  // Run all checks
  allChecksPass &= checkDependencies();
  allChecksPass &= checkPrismaSetup();
  allChecksPass &= checkVercelConfig();
  allChecksPass &= checkEnvironmentVariables();
  
  if (allChecksPass) {
    log('\n🎉 All pre-deployment checks passed!', 'green');
    log('\nNext steps:', 'cyan');
    log('1. Push your code to GitHub', 'yellow');
    log('2. Import project to Vercel', 'yellow');
    log('3. Set environment variables in Vercel', 'yellow');
    log('4. Deploy database schema with: npx prisma db push', 'yellow');
    log('5. Test your deployment', 'yellow');
    
    return true;
  } else {
    log('\n❌ Some checks failed. Please fix the issues above before deploying.', 'red');
    return false;
  }
}

function deployDatabaseSchema() {
  log('\n🗄️  Deploying database schema...', 'cyan');
  
  if (!process.env.DATABASE_URL) {
    log('❌ DATABASE_URL not set. Cannot deploy schema.', 'red');
    log('Set DATABASE_URL environment variable and try again.', 'yellow');
    return false;
  }
  
  try {
    execCommand('npx prisma db push', 'Push database schema');
    log('✅ Database schema deployed successfully', 'green');
    return true;
  } catch (error) {
    log('❌ Failed to deploy database schema', 'red');
    return false;
  }
}

function seedDatabase() {
  log('\n🌱 Seeding database with sample data...', 'cyan');
  
  const seedScript = path.join(process.cwd(), 'scripts', 'populate-sample-data.js');
  if (!fs.existsSync(seedScript)) {
    log('⚠️  Seed script not found. Skipping database seeding.', 'yellow');
    return true;
  }
  
  try {
    execCommand('node scripts/populate-sample-data.js', 'Seed database');
    log('✅ Database seeded successfully', 'green');
    return true;
  } catch (error) {
    log('❌ Failed to seed database', 'red');
    return false;
  }
}

// Main execution
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    log('AstroConnect Vercel Deployment Helper', 'bright');
    log('\nUsage:', 'cyan');
    log('  node scripts/deploy-vercel.js [options]', 'yellow');
    log('\nOptions:', 'cyan');
    log('  --check, -c     Run pre-deployment checks only', 'yellow');
    log('  --schema, -s    Deploy database schema only', 'yellow');
    log('  --seed          Seed database with sample data', 'yellow');
    log('  --help, -h      Show this help message', 'yellow');
    return;
  }
  
  if (args.includes('--check') || args.includes('-c')) {
    runPreDeploymentChecks();
    return;
  }
  
  if (args.includes('--schema') || args.includes('-s')) {
    deployDatabaseSchema();
    return;
  }
  
  if (args.includes('--seed')) {
    seedDatabase();
    return;
  }
  
  // Default: run all checks
  const checksPass = runPreDeploymentChecks();
  
  if (checksPass) {
    log('\n🚀 Ready for Vercel deployment!', 'green');
    log('\nRun with --schema to deploy database schema after Vercel deployment.', 'cyan');
  }
}

// Run the script
main();
